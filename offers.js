/**
 * Offers API Module
 * Handles fetching and displaying offers with images from the Laravel backend
 */

class OffersAPI {
    constructor(baseUrl = 'http://localhost:8000/api') {
        this.baseUrl = baseUrl;
        this.imagePath = 'file:///C:/Users/<USER>/Desktop/agence_backend/public/storage/';
    }

    /**
     * Get authentication token from localStorage
     */
    getAuthToken() {
        return localStorage.getItem('auth_token');
    }

    /**
     * Get authentication headers
     */
    getAuthHeaders() {
        const token = this.getAuthToken();
        const headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        };

        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }

        return headers;
    }

    /**
     * Fetch all offers from the API
     * @returns {Promise<Array>} Array of offers
     */
    async getAllOffers() {
        try {
            console.log('Fetching offers from API...');

            const response = await fetch(`${this.baseUrl}/offers`, {
                method: 'GET',
                headers: this.getAuthHeaders()
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            console.log('API Response:', data);

            // Extract offers from different response structures
            let offers;
            if (data.status === 'success' && data.data) {
                offers = data.data;
            } else if (data.data) {
                offers = data.data;
            } else if (Array.isArray(data)) {
                offers = data;
            } else {
                offers = data;
            }

            console.log('Extracted offers:', offers);
            return Array.isArray(offers) ? offers : [];

        } catch (error) {
            console.error('Error fetching offers:', error);
            throw error;
        }
    }

    /**
     * Fetch a specific offer by ID
     * @param {number} offerId - The offer ID
     * @returns {Promise<Object>} Offer object
     */
    async getOfferById(offerId) {
        try {
            const response = await fetch(`${this.baseUrl}/offers/${offerId}`, {
                method: 'GET',
                headers: this.getAuthHeaders()
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data.data || data;

        } catch (error) {
            console.error('Error fetching offer:', error);
            throw error;
        }
    }

    /**
     * Reserve an offer
     * @param {number} offerId - The offer ID to reserve
     * @returns {Promise<Object>} Reservation response
     */
    async reserveOffer(offerId) {
        try {
            const response = await fetch(`${this.baseUrl}/offers/${offerId}/reserve`, {
                method: 'POST',
                headers: this.getAuthHeaders()
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data;

        } catch (error) {
            console.error('Error making reservation:', error);
            throw error;
        }
    }

    /**
     * Create image path for display
     * @param {string} imagePath - The image path from API
     * @returns {string} Full image path
     */
    createImagePath(imagePath) {
        return `${this.imagePath}${imagePath}`;
    }

    /**
     * Display offers in a container element
     * @param {Array} offers - Array of offers
     * @param {HTMLElement} container - Container element to display offers
     * @param {Object} options - Display options
     */
    displayOffers(offers, container, options = {}) {
        const {
            showImages = true,
            showDetails = true,
            gridCols = 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
            cardClass = 'border rounded-lg p-4 bg-white shadow-md'
        } = options;

        // Clear previous content
        container.innerHTML = '';

        if (!Array.isArray(offers) || offers.length === 0) {
            container.innerHTML = '<p class="text-gray-500 text-center">No offers available.</p>';
            return;
        }

        // Create grid container
        const gridContainer = document.createElement('div');
        gridContainer.className = `grid ${gridCols} gap-4`;

        offers.forEach((offer, index) => {
            console.log(`Displaying offer ${index}:`, offer);

            const offerCard = document.createElement('div');
            offerCard.className = cardClass;

            let cardHTML = `
                <h3 class="font-semibold text-lg mb-2">${offer.titre || offer.title || 'Untitled'}</h3>
            `;

            // Add offer details if requested
            if (showDetails) {
                cardHTML += `
                    <div class="mb-4">
                        ${offer.description ? `<p class="text-gray-600 mb-2">${offer.description}</p>` : ''}
                        ${offer.prix ? `<p class="text-green-600 font-semibold">Price: ${offer.prix} DA</p>` : ''}
                        ${offer.destination ? `<p class="text-blue-600">Destination: ${offer.destination}</p>` : ''}
                        ${offer.date_debut ? `<p class="text-sm text-gray-500">Start: ${offer.date_debut}</p>` : ''}
                        ${offer.date_fin ? `<p class="text-sm text-gray-500">End: ${offer.date_fin}</p>` : ''}
                    </div>
                `;
            }

            // Add images if available and requested
            if (showImages && offer.images && Array.isArray(offer.images)) {
                cardHTML += '<div class="images-container">';

                offer.images.forEach((image, imgIndex) => {
                    console.log(`Image ${imgIndex}:`, image);

                    const imagePath = this.createImagePath(image.image_path);
                    console.log('Constructed image path:', imagePath);

                    cardHTML += `
                        <div class="mb-2">
                            <img src="${imagePath}"
                                 alt="${image.alt_text || 'Offer image'}"
                                 class="w-full h-48 object-cover rounded mb-1"
                                 onerror="this.src='https://via.placeholder.com/400x300?text=Image+Not+Found'; this.classList.add('border-red-500');">
                            <p class="text-xs text-gray-500">Path: ${image.image_path}</p>
                            ${image.is_primary ? '<span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Primary</span>' : ''}
                        </div>
                    `;
                });

                cardHTML += '</div>';
            } else if (showImages) {
                cardHTML += '<p class="text-gray-400 text-sm">No images available</p>';
            }

            offerCard.innerHTML = cardHTML;
            gridContainer.appendChild(offerCard);
        });

        container.appendChild(gridContainer);
    }

    /**
     * Display offers with loading and error handling
     * @param {HTMLElement} container - Container element
     * @param {HTMLElement} loadingElement - Loading indicator element
     * @param {HTMLElement} errorElement - Error display element
     * @param {Object} options - Display options
     */
    async displayOffersWithLoading(container, loadingElement = null, errorElement = null, options = {}) {
        try {
            // Show loading
            if (loadingElement) {
                loadingElement.classList.remove('hidden');
            }

            // Hide error
            if (errorElement) {
                errorElement.classList.add('hidden');
            }

            // Fetch and display offers
            const offers = await this.getAllOffers();
            this.displayOffers(offers, container, options);

        } catch (error) {
            console.error('Error displaying offers:', error);

            // Show error
            if (errorElement) {
                errorElement.classList.remove('hidden');
                const errorMessage = errorElement.querySelector('[data-error-message]') || errorElement;
                errorMessage.textContent = error.message;
            }

            // Display error in container
            container.innerHTML = `
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <p>Error loading offers: ${error.message}</p>
                </div>
            `;

        } finally {
            // Hide loading
            if (loadingElement) {
                loadingElement.classList.add('hidden');
            }
        }
    }

    /**
     * Create a simple offers display with built-in loading and error handling
     * @param {HTMLElement} container - Container element
     * @param {Object} options - Display options
     */
    async createOffersDisplay(container, options = {}) {
        // Create loading element
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'flex justify-center items-center py-8';
        loadingDiv.innerHTML = `
            <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            <span class="ml-2">Loading offers...</span>
        `;

        // Create offers container
        const offersContainer = document.createElement('div');
        offersContainer.className = 'offers-container';

        // Add elements to main container
        container.appendChild(loadingDiv);
        container.appendChild(offersContainer);

        // Load and display offers
        await this.displayOffersWithLoading(offersContainer, loadingDiv, null, options);
    }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OffersAPI;
}

// Make available globally
window.OffersAPI = OffersAPI;

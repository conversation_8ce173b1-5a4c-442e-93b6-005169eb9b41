<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Fix Verification</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 class="text-3xl font-bold mb-6 text-center">🔧 Authentication Fix Verification</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Token Status -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h2 class="text-xl font-semibold mb-4">📋 Token Status</h2>
                <div id="token-status" class="space-y-2"></div>
                <button onclick="checkTokens()" class="mt-4 bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    Refresh Token Check
                </button>
            </div>

            <!-- API Tests -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h2 class="text-xl font-semibold mb-4">🧪 API Tests</h2>
                <div class="space-y-2">
                    <button onclick="testAuthAPI()" class="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                        Test /api/auth/me
                    </button>
                    <button onclick="testOffersAPI()" class="w-full bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                        Test /api/offers
                    </button>
                    <button onclick="testReservationAPI()" class="w-full bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                        Test Reservation API
                    </button>
                </div>
                <div id="api-results" class="mt-4 space-y-2"></div>
            </div>
        </div>

        <!-- Detailed Results -->
        <div class="mt-8 bg-gray-50 p-4 rounded-lg">
            <h2 class="text-xl font-semibold mb-4">📊 Detailed Results</h2>
            <div id="detailed-results" class="bg-white p-4 rounded border max-h-96 overflow-y-auto"></div>
        </div>

        <!-- Fix Status -->
        <div class="mt-6 p-4 rounded-lg" id="fix-status">
            <h2 class="text-xl font-semibold mb-2">✅ Authentication Fix Applied</h2>
            <p class="text-gray-700">
                The token storage key mismatch has been fixed. The system now consistently uses 'auth_token' 
                for storing and retrieving JWT tokens across all components.
            </p>
            <div class="mt-4 text-sm text-gray-600">
                <strong>Fixed files:</strong>
                <ul class="list-disc list-inside mt-2">
                    <li>reservation/checkvisa.html - Updated token retrieval</li>
                    <li>offers.js - Updated OffersAPI.getAuthToken() method</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('detailed-results');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = {
                'success': 'text-green-600',
                'error': 'text-red-600',
                'warning': 'text-orange-600',
                'info': 'text-blue-600'
            }[type] || 'text-gray-600';
            
            resultsDiv.innerHTML += `<div class="${colorClass} text-sm">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function checkTokens() {
            const authToken = localStorage.getItem('auth_token');
            const accessToken = localStorage.getItem('access_token');
            const userData = localStorage.getItem('user_data');
            const userRole = localStorage.getItem('user_role');
            
            const statusDiv = document.getElementById('token-status');
            statusDiv.innerHTML = `
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between">
                        <span>auth_token:</span>
                        <span class="${authToken ? 'text-green-600' : 'text-red-600'}">
                            ${authToken ? '✅ Present' : '❌ Missing'}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span>access_token:</span>
                        <span class="${accessToken ? 'text-orange-600' : 'text-gray-400'}">
                            ${accessToken ? '⚠️ Present (legacy)' : '✅ Not present'}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span>user_data:</span>
                        <span class="${userData ? 'text-green-600' : 'text-red-600'}">
                            ${userData ? '✅ Present' : '❌ Missing'}
                        </span>
                    </div>
                    <div class="flex justify-between">
                        <span>user_role:</span>
                        <span class="${userRole ? 'text-green-600' : 'text-red-600'}">
                            ${userRole || '❌ Missing'}
                        </span>
                    </div>
                </div>
            `;

            addResult(`Token check completed. auth_token: ${authToken ? 'Present' : 'Missing'}`, authToken ? 'success' : 'error');
        }

        async function testAuthAPI() {
            const token = localStorage.getItem('auth_token');
            
            if (!token) {
                addResult('❌ No auth_token found. Please login first.', 'error');
                return;
            }

            try {
                addResult('🧪 Testing /api/auth/me endpoint...', 'info');
                const response = await fetch('http://localhost:8000/api/auth/me', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    addResult(`✅ Auth API test successful! User: ${data.name || data.data?.name || 'Unknown'}`, 'success');
                } else {
                    addResult(`❌ Auth API test failed (${response.status}): ${data.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Auth API network error: ${error.message}`, 'error');
            }
        }

        async function testOffersAPI() {
            const token = localStorage.getItem('auth_token');
            
            try {
                addResult('🧪 Testing /api/offers endpoint...', 'info');
                const response = await fetch('http://localhost:8000/api/offers', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json'
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    const offers = data.data || data;
                    addResult(`✅ Offers API test successful! Found ${offers.length} offers`, 'success');
                } else {
                    addResult(`❌ Offers API test failed (${response.status}): ${data.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Offers API network error: ${error.message}`, 'error');
            }
        }

        async function testReservationAPI() {
            const token = localStorage.getItem('auth_token');
            
            if (!token) {
                addResult('❌ No auth_token found. Cannot test reservation API.', 'error');
                return;
            }

            try {
                addResult('🧪 Testing reservation API (getting offers first)...', 'info');
                
                // First get offers to find a valid offer ID
                const offersResponse = await fetch('http://localhost:8000/api/offers', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json'
                    }
                });

                if (!offersResponse.ok) {
                    addResult(`❌ Cannot get offers for reservation test (${offersResponse.status})`, 'error');
                    return;
                }

                const offersData = await offersResponse.json();
                const offers = offersData.data || offersData;

                if (!offers || offers.length === 0) {
                    addResult('⚠️ No offers available to test reservation', 'warning');
                    return;
                }

                const firstOffer = offers[0];
                addResult(`🎯 Testing reservation for offer: ${firstOffer.titre}`, 'info');

                // Test the reservation endpoint (this might fail but we want to see the response)
                const reservationResponse = await fetch(`http://localhost:8000/api/offers/${firstOffer.id}/reserve`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                const reservationData = await reservationResponse.json();

                if (reservationResponse.ok) {
                    addResult(`✅ Reservation API test successful!`, 'success');
                } else {
                    addResult(`⚠️ Reservation API responded (${reservationResponse.status}): ${reservationData.message || 'Unknown error'}`, 'warning');
                    addResult(`ℹ️ This might be expected (e.g., already reserved, validation errors)`, 'info');
                }

            } catch (error) {
                addResult(`❌ Reservation API network error: ${error.message}`, 'error');
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkTokens();
            addResult('🚀 Authentication fix verification tool loaded', 'success');
            addResult('ℹ️ This tool verifies that the token storage key mismatch has been fixed', 'info');
        });
    </script>
</body>
</html>

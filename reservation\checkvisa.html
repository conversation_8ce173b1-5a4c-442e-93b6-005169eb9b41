<!DOCTYPE html>
<html lang="en" class="light">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Visa Checker - AgencyMo</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    .nav-link {
        @apply text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400;
    }
    .mobile-nav-link {
        @apply text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700;
    }
  </style>
  <!-- Load Navbar Script -->
  <script src="/components/load-navbar.js"></script>
</head>
<body class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white font-sans">
  <!-- Navbar Container -->
  <div id="navbar-container"></div>

  <div class="max-w-4xl mx-auto px-4 py-12">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8">
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Visa Checker</h1>
        <p class="text-gray-600 dark:text-gray-300">
          Upload your visa document image to verify eligibility for international travel.
        </p>
      </div>

      <div class="flex flex-col items-center justify-center space-y-6">
        <div class="w-full max-w-md">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Upload Visa Document</label>
          <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md">
            <div class="space-y-1 text-center">
              <i class="fas fa-file-upload mx-auto h-12 w-12 text-gray-400"></i>
              <div class="flex text-sm text-gray-600 dark:text-gray-400">
                <label for="imageInput" class="relative cursor-pointer bg-white dark:bg-gray-700 rounded-md font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 focus-within:outline-none">
                  <span>Upload a file</span>
                  <input id="imageInput" name="file" type="file" class="sr-only" accept="image/*">
                </label>
                <p class="pl-1">or drag and drop</p>
              </div>
              <p class="text-xs text-gray-500 dark:text-gray-400">
                PNG, JPG, GIF up to 10MB
              </p>
            </div>
          </div>
        </div>

        <button type="button" onclick="checkVisa()" class="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <i class="fas fa-check-circle mr-2"></i> Check Visa
        </button>

        <div id="loader" class="hidden flex items-center justify-center">
          <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          <span class="ml-3 text-gray-600 dark:text-gray-300">Processing...</span>
        </div>

        <div id="result" class="w-full max-w-md p-4 rounded-md"></div>

        <div id="continue-section" class="hidden w-full max-w-md text-center mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
          <button type="button" id="continue-btn" class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
            <i class="fas fa-arrow-right mr-2"></i> Continue Reservation
          </button>
        </div>

        <div id="cancel-section" class="w-full max-w-md text-center mt-4">
          <button type="button" id="cancel-btn" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300">
            <i class="fas fa-times mr-1"></i> Cancel and return to offers
          </button>
        </div>
      </div>
    </div>
  </div>

  <script>
    const API_KEY = "AIzaSyB9V45g5Ax-Voqjw1J7X61P2gJqkWagS_4";
    const API_URL = `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${API_KEY}`;
    let visaVerified = false;

    // Initialize page
    document.addEventListener('DOMContentLoaded', async function() {
      // Add event listener for continue button
      document.getElementById('continue-btn').addEventListener('click', continueReservation);

      // Add event listener for cancel button
      document.getElementById('cancel-btn').addEventListener('click', function() {
        window.location.href = '/offers/offers.html';
      });

      // Get and display offer destination
      await loadOfferDestination();
    });

    // Load offer destination information
    async function loadOfferDestination() {
      const offerId = localStorage.getItem('pending_reservation_offer_id');
      if (!offerId) {
        console.error('No offer ID found in localStorage');
        // Try to redirect back to offers page
        setTimeout(() => {
          window.location.href = '/offers/offers.html';
        }, 2000);
        return;
      }

      try {
        const token = localStorage.getItem('access_token');
        const response = await fetch(`http://localhost:8000/api/offers/${offerId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          const offer = data.data || data;
          if (offer && offer.destination) {
            // Store destination for comparison
            localStorage.setItem('pending_reservation_destination', offer.destination);

            // Update page title to show destination
            const titleElement = document.querySelector('h1');
            if (titleElement) {
              titleElement.innerHTML = `Visa Checker - ${offer.destination}`;
            }

            // Update description
            const descElement = document.querySelector('.text-gray-600');
            if (descElement) {
              descElement.innerHTML = `Upload your visa document for <strong>${offer.destination}</strong> to verify eligibility for this destination.`;
            }

            console.log('Destination loaded successfully:', offer.destination);
          } else {
            console.error('No destination found in offer data');
          }
        } else {
          console.error('Failed to fetch offer data:', response.status);
        }
      } catch (error) {
        console.error('Error loading offer destination:', error);
      }
    }

    async function checkVisa() {
      const fileInput = document.getElementById('imageInput');
      const file = fileInput.files[0];
      const resultDiv = document.getElementById('result');
      const loader = document.getElementById('loader');
      const continueSection = document.getElementById('continue-section');

      if (!file) {
        alert("Please upload an image.");
        return;
      }

      // Get the offer destination from localStorage
      const offerDestination = localStorage.getItem('pending_reservation_destination');
      console.log('Checking visa for destination:', offerDestination);

      if (!offerDestination) {
        console.error('No destination found in localStorage');
        alert("Error: Destination information not found. Please try again.");
        window.location.href = '/offers/offers.html';
        return;
      }

      // Show loading state
      loader.classList.remove('hidden');
      resultDiv.innerHTML = "";
      continueSection.classList.add('hidden');
      visaVerified = false;

      try {
        const base64 = await toBase64(file);

        const response = await fetch(API_URL, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            contents: [{
              parts: [{
                text: `Analyze this image carefully and determine:

1. Is this a VISA document? (passport visa page, visa sticker, visa stamp, or official visa document)
2. If it is a visa, what country/destination is it for?

The user is trying to book a trip to: ${offerDestination}

Please respond EXACTLY in this format:
- Document Type: [VISA/NOT_VISA]
- Destination: [Country/Region name from the visa document, or "N/A" if not a visa]
- Match: [YES/NO] (Does the visa destination match ${offerDestination}? Only YES if both document is a visa AND destination matches)

Be strict: only respond "Document Type: VISA" if you can clearly see visa stamps, visa stickers, or official visa documents. Regular passports, IDs, or other documents should be "NOT_VISA".`,
              }, {
                inline_data: {
                  mime_type: file.type,
                  data: base64
                }
              }]
            }]
          })
        });

        const result = await response.json();
        const text = result?.candidates?.[0]?.content?.parts?.[0]?.text || "";

        console.log("AI Response:", text);

        // Parse the AI response
        const isVisa = text.toLowerCase().includes("document type: visa");
        const isNotVisa = text.toLowerCase().includes("document type: not_visa");
        const destinationMatch = text.toLowerCase().includes("match: yes");

        if (isVisa && destinationMatch) {
          // Valid visa for the correct destination
          resultDiv.className = "w-full max-w-md p-4 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-md";
          resultDiv.innerHTML = `
            <div class="flex items-center">
              <i class="fas fa-check-circle text-2xl mr-3 text-green-600"></i>
              <div>
                <h3 class="font-bold text-lg">✅ Visa Verified</h3>
                <p class="text-sm mt-1">Valid visa document for ${offerDestination}</p>
              </div>
            </div>
          `;
          continueSection.classList.remove('hidden');
          visaVerified = true;

          // Store successful visa check
          localStorage.setItem('last_visa_check', 'verified');
          localStorage.setItem('visa_verification_time', new Date().toISOString());
        } else if (isVisa && !destinationMatch) {
          // Valid visa but wrong destination
          resultDiv.className = "w-full max-w-md p-4 bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 rounded-md";
          resultDiv.innerHTML = `
            <div class="flex items-center">
              <i class="fas fa-exclamation-triangle text-2xl mr-3 text-orange-600"></i>
              <div>
                <h3 class="font-bold text-lg">⚠️ Wrong Destination</h3>
                <p class="text-sm mt-1">This visa is not for ${offerDestination}</p>
                <p class="text-xs mt-1 text-orange-600">Please upload a visa for the correct destination</p>
              </div>
            </div>
          `;
          localStorage.setItem('last_visa_check', 'wrong_destination');
        } else if (isNotVisa) {
          // Not a visa document
          resultDiv.className = "w-full max-w-md p-4 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-md";
          resultDiv.innerHTML = `
            <div class="flex items-center">
              <i class="fas fa-times-circle text-2xl mr-3 text-red-600"></i>
              <div>
                <h3 class="font-bold text-lg" style="color: red;">❌ Not a Visa</h3>
                <p class="text-sm mt-1">The uploaded document is not a valid visa</p>
                <p class="text-xs mt-1 text-red-600">Please upload a valid visa document</p>
              </div>
            </div>
          `;
          localStorage.setItem('last_visa_check', 'not_visa');
        } else {
          // Unclear response
          resultDiv.className = "w-full max-w-md p-4 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-md";
          resultDiv.innerHTML = `
            <div class="flex items-center">
              <i class="fas fa-question-circle text-2xl mr-3 text-gray-600"></i>
              <div>
                <h3 class="font-bold text-lg">❓ Unable to Verify</h3>
                <p class="text-sm mt-1">Could not clearly identify the document</p>
                <p class="text-xs mt-1 text-gray-600">Please try uploading a clearer image</p>
              </div>
            </div>
          `;
          localStorage.setItem('last_visa_check', 'unclear');
        }
      } catch (error) {
        console.error('Error:', error);
        resultDiv.className = "w-full max-w-md p-4 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-md";
        resultDiv.innerHTML = `
          <div class="flex items-center">
            <i class="fas fa-exclamation-circle text-2xl mr-3 text-red-600"></i>
            <div>
              <h3 class="font-bold text-lg">Error Processing Image</h3>
              <p class="text-sm mt-1">${error.message}</p>
            </div>
          </div>
        `;
      } finally {
        loader.classList.add('hidden');
      }
    }

    function toBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result.split(',')[1]);
        reader.onerror = error => reject(error);
      });
    }



    function continueReservation() {
      if (!visaVerified) {
        alert("Please verify your visa document first before continuing with the reservation.");
        return;
      }

      const offerId = localStorage.getItem('pending_reservation_offer_id');
      if (!offerId) {
        alert("Error: Offer information not found. Please try again.");
        window.location.href = '/offers/offers.html';
        return;
      }

      // Make the reservation API call
      makeReservation(offerId);
    }

    async function makeReservation(offerId) {
      try {
        // Show loading state
        const loader = document.getElementById('loader');
        const continueBtn = document.getElementById('continue-btn');
        const resultDiv = document.getElementById('result');

        loader.classList.remove('hidden');
        continueBtn.disabled = true;
        continueBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Processing...';

        const token = localStorage.getItem('access_token');

        // Call the reservation API
        const response = await fetch(`http://localhost:8000/api/offers/${offerId}/reserve`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        });

        // Parse the response
        const data = await response.json();

        // Check if the request was successful
        if (response.ok) {
            resultDiv.className = "w-full max-w-md p-4 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-md";
            resultDiv.innerHTML = `
              <div class="flex items-center">
                <i class="fas fa-check-circle text-2xl mr-3 text-green-600"></i>
                <div>
                  <h3 class="font-bold text-lg">✅ Reservation Confirmed!</h3>
                  <p class="text-sm mt-1">Your visa has been verified and reservation is complete.</p>
                </div>
              </div>
            `;

            // Store successful reservation status
            localStorage.setItem('last_reservation_status', 'success');
            localStorage.setItem('last_visa_check', 'verified');
            localStorage.setItem('visa_verification_time', new Date().toISOString());

            // Clear the pending reservation data
            localStorage.removeItem('pending_reservation_offer_id');
            localStorage.removeItem('pending_reservation_destination');

            // Redirect back to offers page after a delay
            setTimeout(() => {
                window.location.href = '/offers/offers.html';
            }, 4000);
        } else {
            resultDiv.className = "w-full max-w-md p-4 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-md";
            resultDiv.innerHTML = `
              <div class="flex items-center">
                <i class="fas fa-times-circle text-2xl mr-3 text-red-600"></i>
                <div>
                  <h3 class="font-bold text-lg">❌ Reservation Failed</h3>
                  <p class="text-sm mt-1">${data.message || 'Unknown error occurred during reservation'}</p>
                </div>
              </div>
            `;

            // Store failed reservation status
            localStorage.setItem('last_reservation_status', 'failed');
            localStorage.setItem('last_visa_check', 'verified'); // Visa was verified but reservation failed

            continueBtn.disabled = false;
            continueBtn.innerHTML = '<i class="fas fa-redo mr-2"></i> Try Again';
        }
      } catch (error) {
        console.error('Error making reservation:', error);
        const resultDiv = document.getElementById('result');
        resultDiv.className = "w-full max-w-md p-4 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-md";
        resultDiv.innerHTML = `
          <div class="flex items-center">
            <i class="fas fa-exclamation-circle text-2xl mr-3 text-red-600"></i>
            <div>
              <h3 class="font-bold text-lg">🚫 Connection Error</h3>
              <p class="text-sm mt-1">${error.message}</p>
            </div>
          </div>
        `;

        // Store network error status
        localStorage.setItem('last_reservation_status', 'network_error');
        localStorage.setItem('last_visa_check', 'verified'); // Visa was verified but network failed

        const continueBtn = document.getElementById('continue-btn');
        continueBtn.disabled = false;
        continueBtn.innerHTML = '<i class="fas fa-redo mr-2"></i> Try Again';
      } finally {
        // Hide loading state
        document.getElementById('loader').classList.add('hidden');
      }
    }
  </script>
</body>
</html>
